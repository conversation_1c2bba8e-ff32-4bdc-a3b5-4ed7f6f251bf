#!/usr/bin/env python3
"""
Example usage of the Raster<PERSON>eader class.
"""

from raster_reader import <PERSON><PERSON><PERSON><PERSON><PERSON>

def main():
    # Initialize raster reader
    raster = RasterReader("raster/ET-.tif")
    
    print("🌍 RASTER ANALYSIS EXAMPLE")
    print("=" * 40)
    
    # 1. Get basic raster information
    print("\n📊 RASTER INFO:")
    info = raster.get_info()
    print(f"   Dimensions: {info['dimensions']}")
    print(f"   Total pixels: {info['total_pixels']:,}")
    print(f"   Data type: {info['data_type']}")
    
    # 2. Get statistics
    print("\n📈 STATISTICS:")
    stats = raster.get_statistics()
    print(f"   Valid pixels: {stats['valid_pixels']:,} ({stats['valid_percentage']:.1f}%)")
    print(f"   Value range: {stats['min_value']:.1f} to {stats['max_value']:.1f}")
    print(f"   Average: {stats['mean_value']:.1f}")
    
    # 3. Query by coordinates (lat, lon)
    print("\n🎯 COORDINATE QUERIES:")
    
    # Test coordinates in Pakistan
    test_coords = [
        (31.5204, 74.3587),  # Lahore
        (32.0, 72.0),        # Central Pakistan
        (30.0, 71.5),        # Test point
        (29.5, 70.8),        # Another test point
    ]
    
    results = raster.query_multiple_coords(test_coords)
    
    for result in results:
        if result['is_valid']:
            print(f"   ({result['lat']}, {result['lon']}): {result['pixel_value']:.2f} mm/day")
        else:
            status = "Out of bounds" if not result['within_bounds'] else "NoData"
            print(f"   ({result['lat']}, {result['lon']}): {status}")
    
    # 4. Query by row/column
    print("\n🔢 ROW/COLUMN QUERIES:")
    
    # Test some pixel positions
    pixel_tests = [
        (1000, 1500),  # Random position
        (1758, 2059),  # Maximum value location
        (500, 2000),   # Another position
    ]
    
    for row, col in pixel_tests:
        result = raster.get_pixel_value_by_rowcol(row, col)
        if result['is_valid']:
            print(f"   Row {row}, Col {col}: {result['pixel_value']:.2f} mm/day")
            print(f"     Location: ({result['lat']:.4f}°N, {result['lon']:.4f}°E)")
        else:
            print(f"   Row {row}, Col {col}: No valid data")
    
    # 5. Find extreme values
    print("\n🏔️ EXTREME VALUES:")
    extremes = raster.find_extreme_values()
    print(f"   Minimum: {extremes['minimum']['value']:.2f} mm/day")
    print(f"     at ({extremes['minimum']['lat']:.4f}°N, {extremes['minimum']['lon']:.4f}°E)")
    print(f"   Maximum: {extremes['maximum']['value']:.2f} mm/day")
    print(f"     at ({extremes['maximum']['lat']:.4f}°N, {extremes['maximum']['lon']:.4f}°E)")
    
    # 6. Save results to JSON
    print("\n💾 SAVING RESULTS:")
    output_file = raster.save_results_to_json(results, "results.json")
    print(f"   ✅ Results saved to {output_file}")
    
    # 7. Summary
    valid_results = [r for r in results if r['is_valid']]
    print(f"\n📋 SUMMARY:")
    print(f"   Total queries: {len(results)}")
    print(f"   Valid results: {len(valid_results)}")
    print(f"   Success rate: {len(valid_results)/len(results)*100:.1f}%")
    
    if valid_results:
        values = [r['pixel_value'] for r in valid_results]
        print(f"   ET range in queries: {min(values):.2f} - {max(values):.2f} mm/day")

if __name__ == "__main__":
    main()
