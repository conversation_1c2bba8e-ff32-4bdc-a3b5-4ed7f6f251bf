import rasterio
import numpy as np
import matplotlib.pyplot as plt

def analyze_pixel_range(raster_path):
    """
    Comprehensive analysis of pixel value range in the raster.
    
    Parameters:
    raster_path (str): Path to the raster file
    """
    with rasterio.open(raster_path) as src:
        print("=== RASTER PIXEL RANGE ANALYSIS ===")
        print(f"File: {raster_path}")
        print(f"Dimensions: {src.width} x {src.height} pixels")
        print(f"Total pixels: {src.width * src.height:,}")
        print()
        
        # Read the raster data
        raster = src.read(1)
        
        # Separate valid data from NoData
        nodata_mask = raster == src.nodata
        valid_data = raster[~nodata_mask]
        
        print("=== PIXEL VALUE STATISTICS ===")
        print(f"NoData value: {src.nodata}")
        print(f"Valid pixels: {len(valid_data):,}")
        print(f"NoData pixels: {np.sum(nodata_mask):,}")
        print(f"Valid data percentage: {(len(valid_data) / raster.size) * 100:.1f}%")
        print()
        
        if len(valid_data) > 0:
            print("=== VALID PIXEL VALUE RANGE ===")
            print(f"Minimum value: {np.min(valid_data):.6f}")
            print(f"Maximum value: {np.max(valid_data):.6f}")
            print(f"Range: {np.max(valid_data) - np.min(valid_data):.6f}")
            print(f"Mean: {np.mean(valid_data):.6f}")
            print(f"Median: {np.median(valid_data):.6f}")
            print(f"Standard deviation: {np.std(valid_data):.6f}")
            print()
            
            # Percentiles
            percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
            print("=== PERCENTILES ===")
            for p in percentiles:
                value = np.percentile(valid_data, p)
                print(f"{p:2d}th percentile: {value:.6f}")
            print()
            
            # Value distribution analysis
            print("=== VALUE DISTRIBUTION ===")
            negative_count = np.sum(valid_data < 0)
            zero_count = np.sum(valid_data == 0)
            positive_count = np.sum(valid_data > 0)
            
            print(f"Negative values: {negative_count:,} ({(negative_count/len(valid_data)*100):.1f}%)")
            print(f"Zero values: {zero_count:,} ({(zero_count/len(valid_data)*100):.1f}%)")
            print(f"Positive values: {positive_count:,} ({(positive_count/len(valid_data)*100):.1f}%)")
            print()
            
            # Binned analysis
            print("=== VALUE RANGES (BINNED) ===")
            bins = [
                (-float('inf'), 0, "Negative"),
                (0, 10, "Very Low (0-10)"),
                (10, 25, "Low (10-25)"),
                (25, 50, "Medium (25-50)"),
                (50, 75, "High (50-75)"),
                (75, 100, "Very High (75-100)"),
                (100, float('inf'), "Extreme (>100)")
            ]
            
            for min_val, max_val, label in bins:
                if min_val == -float('inf'):
                    mask = valid_data < max_val
                elif max_val == float('inf'):
                    mask = valid_data >= min_val
                else:
                    mask = (valid_data >= min_val) & (valid_data < max_val)
                
                count = np.sum(mask)
                percentage = (count / len(valid_data)) * 100
                print(f"{label:20s}: {count:8,} pixels ({percentage:5.1f}%)")
            
            print()
            
            # Extreme values
            print("=== EXTREME VALUES ===")
            # Find locations of min and max values
            min_idx = np.unravel_index(np.argmin(valid_data), valid_data.shape)
            max_idx = np.unravel_index(np.argmax(valid_data), valid_data.shape)
            
            # Convert back to full raster coordinates
            valid_indices = np.where(~nodata_mask)
            min_row, min_col = valid_indices[0][np.argmin(valid_data)], valid_indices[1][np.argmin(valid_data)]
            max_row, max_col = valid_indices[0][np.argmax(valid_data)], valid_indices[1][np.argmax(valid_data)]
            
            print(f"Minimum value location: Row {min_row}, Col {min_col}")
            print(f"Maximum value location: Row {max_row}, Col {max_col}")
            
            # Convert to geographic coordinates
            min_lon, min_lat = src.xy(min_row, min_col)
            max_lon, max_lat = src.xy(max_row, max_col)
            
            print(f"Minimum value coordinates: {min_lat:.4f}°N, {min_lon:.4f}°E")
            print(f"Maximum value coordinates: {max_lat:.4f}°N, {max_lon:.4f}°E")
            
        else:
            print("No valid data found in the raster!")

if __name__ == "__main__":
    raster_path = "raster/ET-.tif"
    analyze_pixel_range(raster_path)
