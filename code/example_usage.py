#!/usr/bin/env python3
"""
Example script showing how to use the get_pixel_value_from_coords function
and save results to JSON file.
"""

from get_pixel_value import get_pixel_value_from_coords, save_results_to_json
import json

def main():
    # Path to your raster file
    raster_path = "raster/ET-.tif"
    
    # Define your coordinates to query
    coordinates_to_query = [
        (31.5204, 74.3587),  # Lahore
        (32.0, 72.0),        # Central Pakistan
        (30.0, 71.5),        # Another location
        (29.5, 70.8),        # Test point
    ]
    
    print("Querying raster for pixel values...")
    print("=" * 50)
    
    # Store all results
    all_results = []
    
    # Query each coordinate
    for i, (lat, lon) in enumerate(coordinates_to_query, 1):
        print(f"Query {i}: Lat={lat}, Lon={lon}")
        
        result = get_pixel_value_from_coords(raster_path, lat, lon)
        all_results.append(result)
        
        if result['within_bounds']:
            if result['is_valid']:
                print(f"  ✓ ET Value: {result['pixel_value']:.2f} mm/day")
                print(f"  Pixel: Row {result['row']}, Col {result['col']}")
            else:
                print(f"  ✗ NoData at this location")
        else:
            print(f"  ✗ Outside raster bounds")
        print()
    
    # Save results to JSON file (overwrites existing file)
    output_file = "results.json"
    save_results_to_json(all_results, output_file)
    
    print(f"Results saved to {output_file}")
    
    # Show summary
    valid_results = [r for r in all_results if r['is_valid']]
    print(f"\nSummary:")
    print(f"  Total queries: {len(all_results)}")
    print(f"  Valid results: {len(valid_results)}")
    print(f"  Success rate: {len(valid_results)/len(all_results)*100:.1f}%")
    
    if valid_results:
        values = [r['pixel_value'] for r in valid_results]
        print(f"  ET range: {min(values):.2f} - {max(values):.2f} mm/day")
        print(f"  Average ET: {sum(values)/len(values):.2f} mm/day")

if __name__ == "__main__":
    main()
