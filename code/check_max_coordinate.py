from get_pixel_value import get_pixel_value_from_coords

# Coordinates of maximum pixel value from analysis
max_lat = 30.1271
max_lon = 73.6594

raster_path = "raster/ET-.tif"

print("=== VERIFYING MAXIMUM PIXEL VALUE COORDINATE ===")
print(f"Querying coordinate: {max_lat}°N, {max_lon}°E")
print()

result = get_pixel_value_from_coords(raster_path, max_lat, max_lon)

print(f"Latitude: {result['lat']}")
print(f"Longitude: {result['lon']}")
print(f"Pixel position: Row {result['row']}, Col {result['col']}")
print(f"Pixel value: {result['pixel_value']:.6f} mm/day")
print(f"Is valid: {result['is_valid']}")
print(f"Within bounds: {result['within_bounds']}")

if result['is_valid']:
    print(f"\n✅ CONFIRMED: Maximum ET value is {result['pixel_value']:.2f} mm/day")
    print(f"   Location: {max_lat}°N, {max_lon}°E")
    print(f"   This is in Pakistan, likely in Punjab province")
else:
    print("❌ Error: Could not retrieve value at this coordinate")
