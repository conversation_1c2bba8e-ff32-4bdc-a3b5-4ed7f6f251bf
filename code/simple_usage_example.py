#!/usr/bin/env python3
"""
Simple usage example of the RasterReader class.
"""

from raster_reader import <PERSON><PERSON><PERSON><PERSON><PERSON>

def main():
    # Initialize raster reader
    raster = RasterReader("raster/ET-.tif")
    
    print("🌍 RASTER READER - SIMPLE USAGE EXAMPLE")
    print("=" * 50)
    
    # 1. Get basic info
    print("\n📊 RASTER INFO:")
    info = raster.get_info()
    print(f"   File: {info['file_path']}")
    print(f"   Size: {info['dimensions']} pixels")
    print(f"   CRS: {info['coordinate_system']}")
    
    # 2. Get statistics
    print("\n📈 STATISTICS:")
    stats = raster.get_statistics()
    print(f"   Valid pixels: {stats['valid_pixels']:,} ({stats['valid_percentage']:.1f}%)")
    print(f"   Value range: {stats['min_value']:.1f} to {stats['max_value']:.1f}")
    print(f"   Average: {stats['mean_value']:.1f}")
    
    # 3. Query by coordinates (lat, lon)
    print("\n🎯 QUERY BY COORDINATES:")
    
    # Single coordinate
    result = raster.get_pixel_value_by_coords(31.5204, 74.3587)  # Lahore
    if result['is_valid']:
        print(f"   Lahore (31.52°N, 74.36°E): {result['pixel_value']:.2f} mm/day")
    
    # Multiple coordinates
    coords = [(32.0, 72.0), (30.0, 71.5), (29.5, 70.8)]
    results = raster.query_multiple_coords(coords)
    
    for result in results:
        if result['is_valid']:
            print(f"   ({result['lat']}, {result['lon']}): {result['pixel_value']:.2f} mm/day")
    
    # 4. Query by row/column
    print("\n🔢 QUERY BY ROW/COLUMN:")
    
    # Single row/col
    result = raster.get_pixel_value_by_rowcol(1000, 1500)
    if result['is_valid']:
        print(f"   Row 1000, Col 1500: {result['pixel_value']:.2f} mm/day")
        print(f"   Location: ({result['lat']:.4f}°N, {result['lon']:.4f}°E)")
    
    # 5. Find extreme values
    print("\n🏔️ EXTREME VALUES:")
    extremes = raster.find_extreme_values()
    print(f"   Minimum: {extremes['minimum']['value']:.2f} mm/day")
    print(f"   Location: ({extremes['minimum']['lat']:.4f}°N, {extremes['minimum']['lon']:.4f}°E)")
    print(f"   Maximum: {extremes['maximum']['value']:.2f} mm/day")
    print(f"   Location: ({extremes['maximum']['lat']:.4f}°N, {extremes['maximum']['lon']:.4f}°E)")
    
    # 6. Save results to JSON
    print("\n💾 SAVING RESULTS:")
    all_results = results + [result]  # Combine coordinate and row/col results
    output_file = raster.save_results_to_json(all_results, "simple_results.json")
    print(f"   ✅ Saved to {output_file}")

if __name__ == "__main__":
    main()
