import rasterio
import numpy as np
import json
from datetime import datetime
from rasterio.transform import rowcol, xy

class Ra<PERSON><PERSON>eader:
    """
    A simple, clean class for reading raster files and extracting pixel values.
    """
    
    def __init__(self, raster_path):
        """
        Initialize the RasterReader with a raster file.
        
        Parameters:
        raster_path (str): Path to the raster file
        """
        self.raster_path = raster_path
        self._raster_data = None
        self._src_info = None
        self._load_raster()
    
    def _load_raster(self):
        """Load raster data and metadata."""
        with rasterio.open(self.raster_path) as src:
            self._raster_data = src.read(1)  # Read first band
            self._src_info = {
                'width': src.width,
                'height': src.height,
                'count': src.count,
                'dtype': str(src.dtypes[0]),
                'crs': str(src.crs),
                'bounds': src.bounds,
                'transform': src.transform,
                'nodata': src.nodata
            }
    
    def get_info(self):
        """
        Get basic information about the raster.
        
        Returns:
        dict: Dictionary containing raster metadata
        """
        return {
            'file_path': self.raster_path,
            'dimensions': f"{self._src_info['width']} x {self._src_info['height']}",
            'total_pixels': self._src_info['width'] * self._src_info['height'],
            'data_type': self._src_info['dtype'],
            'coordinate_system': self._src_info['crs'],
            'bounds': {
                'left': self._src_info['bounds'].left,
                'bottom': self._src_info['bounds'].bottom,
                'right': self._src_info['bounds'].right,
                'top': self._src_info['bounds'].top
            },
            'nodata_value': self._src_info['nodata']
        }
    
    def get_pixel_value_by_coords(self, lat, lon):
        """
        Get pixel value using latitude and longitude coordinates.
        
        Parameters:
        lat (float): Latitude in decimal degrees
        lon (float): Longitude in decimal degrees
        
        Returns:
        dict: Dictionary containing pixel information
        """
        with rasterio.open(self.raster_path) as src:
            # Convert lat/lon to pixel coordinates
            row, col = rowcol(src.transform, lon, lat)
            
            # Check if coordinates are within bounds
            if 0 <= row < src.height and 0 <= col < src.width:
                pixel_value = self._raster_data[row, col]
                is_valid = pixel_value != self._src_info['nodata'] and not np.isnan(pixel_value)
                
                return {
                    'lat': lat,
                    'lon': lon,
                    'row': int(row),
                    'col': int(col),
                    'pixel_value': float(pixel_value) if is_valid else None,
                    'is_valid': is_valid,
                    'within_bounds': True
                }
            else:
                return {
                    'lat': lat,
                    'lon': lon,
                    'row': int(row) if row is not None else None,
                    'col': int(col) if col is not None else None,
                    'pixel_value': None,
                    'is_valid': False,
                    'within_bounds': False,
                    'error': 'Coordinates outside raster bounds'
                }
    
    def get_pixel_value_by_rowcol(self, row, col):
        """
        Get pixel value using row and column indices.
        
        Parameters:
        row (int): Row index (0-based)
        col (int): Column index (0-based)
        
        Returns:
        dict: Dictionary containing pixel information
        """
        # Check if row/col are within bounds
        if 0 <= row < self._src_info['height'] and 0 <= col < self._src_info['width']:
            pixel_value = self._raster_data[row, col]
            is_valid = pixel_value != self._src_info['nodata'] and not np.isnan(pixel_value)
            
            # Convert pixel coordinates to lat/lon
            with rasterio.open(self.raster_path) as src:
                lon, lat = xy(src.transform, row, col)
            
            return {
                'row': int(row),
                'col': int(col),
                'lat': float(lat),
                'lon': float(lon),
                'pixel_value': float(pixel_value) if is_valid else None,
                'is_valid': is_valid,
                'within_bounds': True
            }
        else:
            return {
                'row': int(row),
                'col': int(col),
                'lat': None,
                'lon': None,
                'pixel_value': None,
                'is_valid': False,
                'within_bounds': False,
                'error': 'Row/Column outside raster bounds'
            }
    
    def get_statistics(self):
        """
        Get statistical information about the raster values.
        
        Returns:
        dict: Dictionary containing statistics
        """
        # Filter out NoData values
        valid_data = self._raster_data[self._raster_data != self._src_info['nodata']]
        
        if len(valid_data) == 0:
            return {'error': 'No valid data found in raster'}
        
        return {
            'total_pixels': self._raster_data.size,
            'valid_pixels': len(valid_data),
            'nodata_pixels': self._raster_data.size - len(valid_data),
            'valid_percentage': (len(valid_data) / self._raster_data.size) * 100,
            'min_value': float(np.min(valid_data)),
            'max_value': float(np.max(valid_data)),
            'mean_value': float(np.mean(valid_data)),
            'median_value': float(np.median(valid_data)),
            'std_deviation': float(np.std(valid_data)),
            'range': float(np.max(valid_data) - np.min(valid_data))
        }
    
    def query_multiple_coords(self, coordinates):
        """
        Query multiple coordinates at once.
        
        Parameters:
        coordinates (list): List of (lat, lon) tuples
        
        Returns:
        list: List of result dictionaries
        """
        results = []
        for lat, lon in coordinates:
            result = self.get_pixel_value_by_coords(lat, lon)
            results.append(result)
        return results
    
    def save_results_to_json(self, results, output_file="results.json"):
        """
        Save query results to JSON file.
        
        Parameters:
        results (list): List of query results
        output_file (str): Output file name
        """
        json_data = {
            'timestamp': datetime.now().isoformat(),
            'raster_file': self.raster_path,
            'total_queries': len(results),
            'queries': results
        }
        
        with open(output_file, 'w') as f:
            json.dump(json_data, f, indent=2)
        
        print(f"Results saved to {output_file}")
        return output_file
    
    def find_extreme_values(self):
        """
        Find locations of minimum and maximum values.
        
        Returns:
        dict: Dictionary containing extreme value locations
        """
        valid_mask = self._raster_data != self._src_info['nodata']
        valid_data = self._raster_data[valid_mask]
        
        if len(valid_data) == 0:
            return {'error': 'No valid data found'}
        
        # Find indices of min and max values
        min_idx = np.unravel_index(np.argmin(self._raster_data[valid_mask]), valid_data.shape)
        max_idx = np.unravel_index(np.argmax(self._raster_data[valid_mask]), valid_data.shape)
        
        # Get actual row, col positions
        valid_indices = np.where(valid_mask)
        min_row, min_col = valid_indices[0][np.argmin(valid_data)], valid_indices[1][np.argmin(valid_data)]
        max_row, max_col = valid_indices[0][np.argmax(valid_data)], valid_indices[1][np.argmax(valid_data)]
        
        # Get coordinate information
        min_result = self.get_pixel_value_by_rowcol(min_row, min_col)
        max_result = self.get_pixel_value_by_rowcol(max_row, max_col)
        
        return {
            'minimum': {
                'value': min_result['pixel_value'],
                'row': min_result['row'],
                'col': min_result['col'],
                'lat': min_result['lat'],
                'lon': min_result['lon']
            },
            'maximum': {
                'value': max_result['pixel_value'],
                'row': max_result['row'],
                'col': max_result['col'],
                'lat': max_result['lat'],
                'lon': max_result['lon']
            }
        }


# Example usage
if __name__ == "__main__":
    # Initialize the raster reader
    raster = RasterReader("raster/ET-.tif")
    
    print("=== RASTER INFO ===")
    info = raster.get_info()
    for key, value in info.items():
        print(f"{key}: {value}")
    
    print("\n=== STATISTICS ===")
    stats = raster.get_statistics()
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"{key}: {value:.2f}")
        else:
            print(f"{key}: {value}")
    
    print("\n=== COORDINATE QUERIES ===")
    # Test coordinates
    test_coords = [
        (31.5204, 74.3587),  # Lahore
        (32.0, 72.0),        # Central Pakistan
        (30.0, 71.5),        # Test point
    ]
    
    results = raster.query_multiple_coords(test_coords)
    for result in results:
        if result['is_valid']:
            print(f"({result['lat']}, {result['lon']}) -> {result['pixel_value']:.2f}")
        else:
            print(f"({result['lat']}, {result['lon']}) -> No valid data")
    
    print("\n=== ROW/COL QUERIES ===")
    # Test row/col queries
    row_col_tests = [(1000, 1500), (500, 2000), (1758, 2059)]  # Last one is max value location
    
    for row, col in row_col_tests:
        result = raster.get_pixel_value_by_rowcol(row, col)
        if result['is_valid']:
            print(f"Row {row}, Col {col} -> {result['pixel_value']:.2f} at ({result['lat']:.4f}, {result['lon']:.4f})")
        else:
            print(f"Row {row}, Col {col} -> No valid data")
    
    print("\n=== EXTREME VALUES ===")
    extremes = raster.find_extreme_values()
    print(f"Minimum: {extremes['minimum']['value']:.2f} at ({extremes['minimum']['lat']:.4f}, {extremes['minimum']['lon']:.4f})")
    print(f"Maximum: {extremes['maximum']['value']:.2f} at ({extremes['maximum']['lat']:.4f}, {extremes['maximum']['lon']:.4f})")
    
    # Save results
    raster.save_results_to_json(results, "raster_query_results.json")
