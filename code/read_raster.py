import rasterio
import numpy as np

raster_path = "raster/ET-.tif"
# Open the raster
with rasterio.open(raster_path) as src:
    print("=== RASTER METADATA ===")
    print(f"Number of bands: {src.count}")
    print(f"Data type: {src.dtypes[0]}")
    print(f"Dimensions: {src.width} x {src.height} (width x height)")
    print(f"CRS: {src.crs}")
    print(f"Transform: {src.transform}")
    print(f"Bounds: {src.bounds}")
    print(f"NoData value: {src.nodata}")
    print()

    print("=== FULL PROFILE ===")
    print(src.profile)
    print()

    # Read the raster data
    raster = src.read(1) # read the first band

print("=== RASTER DATA ANALYSIS ===")
print(f"Shape: {raster.shape}")  # (rows, cols)
print(f"Data type: {raster.dtype}")
print(f"Min value: {np.nanmin(raster)}")
print(f"Max value: {np.nanmax(raster)}")
print(f"Mean value: {np.nanmean(raster)}")
print(f"Standard deviation: {np.nanstd(raster)}")
print(f"Number of valid pixels: {np.count_nonzero(~np.isnan(raster))}")
print(f"Number of NaN/NoData pixels: {np.count_nonzero(np.isnan(raster))}")

# Show a sample of the data
print(f"\nSample values (first 5x5 pixels):")
print(raster[:5, :5])
