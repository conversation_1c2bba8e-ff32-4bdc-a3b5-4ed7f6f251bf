import rasterio
import numpy as np
from rasterio.transform import rowcol

def get_pixel_value_from_coords(raster_path, lat, lon):
    """
    Get pixel value from latitude and longitude coordinates.

    Parameters:
    raster_path (str): Path to the raster file
    lat (float): Latitude in decimal degrees
    lon (float): Longitude in decimal degrees

    Returns:
    dict: Dictionary containing pixel coordinates, value, and metadata
    """
    with rasterio.open(raster_path) as src:
        # Convert lat/lon to pixel coordinates
        row, col = rowcol(src.transform, lon, lat)
        print(row, col)

        # Check if coordinates are within raster bounds
        print(src.height, src.width)
        if 0 <= row < src.height and 0 <= col < src.width:
            # Read the pixel value
            pixel_value = src.read(1)[row, col]

            # Check if it's a valid value (not NoData)
            is_valid = pixel_value != src.nodata and not np.isnan(pixel_value)

            return {
                'lat': lat,
                'lon': lon,
                'row': row,
                'col': col,
                'pixel_value': pixel_value,
                'is_valid': is_valid,
                'nodata_value': src.nodata,
                'within_bounds': True
            }
        else:
            return {
                'lat': lat,
                'lon': lon,
                'row': row,
                'col': col,
                'pixel_value': None,
                'is_valid': False,
                'nodata_value': src.nodata,
                'within_bounds': False,
                'error': 'Coordinates outside raster bounds'
            }

# Load raster for analysis
raster_path = "raster/ET-.tif"
with rasterio.open(raster_path) as src:
    print("=== RASTER METADATA ===")
    print(f"Number of bands: {src.count}")
    print(f"Data type: {src.dtypes[0]}")
    print(f"Dimensions: {src.width} x {src.height} (width x height)")
    print(f"CRS: {src.crs}")
    print(f"Transform: {src.transform}")
    print(f"Bounds: {src.bounds}")
    print(f"NoData value: {src.nodata}")
    print()

    # Read the raster data
    raster = src.read(1)

print("=== RASTER DATA ANALYSIS ===")
print(f"Shape: {raster.shape}")
print(f"Data type: {raster.dtype}")

# Filter out NoData values for statistics
valid_data = raster[raster != src.nodata]
print(f"Min value (valid data): {np.min(valid_data)}")
print(f"Max value (valid data): {np.max(valid_data)}")
print(f"Mean value (valid data): {np.mean(valid_data):.2f}")
print(f"Standard deviation (valid data): {np.std(valid_data):.2f}")
print(f"Number of valid pixels: {len(valid_data)}")
print(f"Number of NoData pixels: {np.count_nonzero(raster == src.nodata)}")

print("\n=== TESTING COORDINATE TO PIXEL FUNCTION ===")
# Test coordinates within Pakistan region
# test_coordinates = [
#     (30.3753, 69.3451),  # Quetta area
#     (31.5204, 74.3587),  # Lahore
#     (33.6844, 73.0479),  # Islamabad
#     (24.8607, 67.0011),  # Karachi
#     (32.0000, 72.0000),  # Central Pakistan
# ]
#
# for lat, lon in test_coordinates:
#     result = get_pixel_value_from_coords(raster_path, lat, lon)
#     print(f"\nCoordinates: ({lat}, {lon})")
#     print(f"  Pixel position: Row {result['row']}, Col {result['col']}")
#     print(f"  Within bounds: {result['within_bounds']}")
#     if result['within_bounds']:
#         print(f"  Pixel value: {result['pixel_value']}")
#         print(f"  Is valid data: {result['is_valid']}")
#         if result['is_valid']:
#             print(f"  ET value: {result['pixel_value']:.2f} mm/day")
#     else:
#         print(f"  Error: {result.get('error', 'Unknown error')}")

test_coordinates = (30.1271, 73.6594)
result = get_pixel_value_from_coords(raster_path, test_coordinates[0], test_coordinates[1])
print(result)
print(result["pixel_value"])
