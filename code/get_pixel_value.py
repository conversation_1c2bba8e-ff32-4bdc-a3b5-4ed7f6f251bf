import rasterio
import numpy as np
from rasterio.transform import rowcol

def get_pixel_value_from_coords(raster_path, lat, lon):
    """
    Get pixel value from latitude and longitude coordinates.
    
    Parameters:
    raster_path (str): Path to the raster file
    lat (float): Latitude in decimal degrees
    lon (float): Longitude in decimal degrees
    
    Returns:
    dict: Dictionary containing pixel coordinates, value, and metadata
    """
    with rasterio.open(raster_path) as src:
        # Convert lat/lon to pixel coordinates
        row, col = rowcol(src.transform, lon, lat)
        
        # Check if coordinates are within raster bounds
        if 0 <= row < src.height and 0 <= col < src.width:
            # Read the pixel value
            pixel_value = src.read(1)[row, col]
            
            # Check if it's a valid value (not NoData)
            is_valid = pixel_value != src.nodata and not np.isnan(pixel_value)
            
            return {
                'lat': lat,
                'lon': lon,
                'row': row,
                'col': col,
                'pixel_value': pixel_value,
                'is_valid': is_valid,
                'nodata_value': src.nodata,
                'within_bounds': True
            }
        else:
            return {
                'lat': lat,
                'lon': lon,
                'row': row,
                'col': col,
                'pixel_value': None,
                'is_valid': False,
                'nodata_value': src.nodata,
                'within_bounds': False,
                'error': 'Coordinates outside raster bounds'
            }

def get_raster_info(raster_path):
    """
    Get basic information about the raster file.
    
    Parameters:
    raster_path (str): Path to the raster file
    
    Returns:
    dict: Dictionary containing raster metadata
    """
    with rasterio.open(raster_path) as src:
        return {
            'width': src.width,
            'height': src.height,
            'count': src.count,
            'dtype': str(src.dtypes[0]),
            'crs': str(src.crs),
            'bounds': src.bounds,
            'transform': src.transform,
            'nodata': src.nodata
        }

# Example usage and testing
if __name__ == "__main__":
    raster_path = "raster/ET-.tif"
    
    print("=== RASTER INFO ===")
    info = get_raster_info(raster_path)
    print(f"Dimensions: {info['width']} x {info['height']}")
    print(f"Bounds: {info['bounds']}")
    print(f"NoData: {info['nodata']}")
    print()
    
    print("=== SAMPLE COORDINATE QUERIES ===")
    
    # Test coordinates
    test_coords = [
        (31.5204, 74.3587),  # Lahore - should be valid
        (32.0, 72.0),        # Central Pakistan - should be valid
        (33.6844, 73.0479),  # Islamabad - might be NoData
        (25.0, 67.0),        # Outside bounds
    ]
    
    for lat, lon in test_coords:
        result = get_pixel_value_from_coords(raster_path, lat, lon)
        
        print(f"Query: Lat={lat}, Lon={lon}")
        print(f"  Pixel position: ({result['row']}, {result['col']})")
        
        if result['within_bounds']:
            if result['is_valid']:
                print(f"  ✓ Valid ET value: {result['pixel_value']:.2f} mm/day")
            else:
                print(f"  ✗ NoData pixel (value: {result['pixel_value']})")
        else:
            print(f"  ✗ Outside raster bounds")
        print()
