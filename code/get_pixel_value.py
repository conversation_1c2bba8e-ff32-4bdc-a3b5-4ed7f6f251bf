import rasterio
import numpy as np
import json
from datetime import datetime
from rasterio.transform import rowcol

def get_pixel_value_from_coords(raster_path, lat, lon):
    """
    Get pixel value from latitude and longitude coordinates.
    
    Parameters:
    raster_path (str): Path to the raster file
    lat (float): Latitude in decimal degrees
    lon (float): Longitude in decimal degrees
    
    Returns:
    dict: Dictionary containing pixel coordinates, value, and metadata
    """
    with rasterio.open(raster_path) as src:
        # Convert lat/lon to pixel coordinates
        row, col = rowcol(src.transform, lon, lat)
        
        # Check if coordinates are within raster bounds
        if 0 <= row < src.height and 0 <= col < src.width:
            # Read the pixel value
            pixel_value = src.read(1)[row, col]
            
            # Check if it's a valid value (not NoData)
            is_valid = pixel_value != src.nodata and not np.isnan(pixel_value)
            
            return {
                'lat': lat,
                'lon': lon,
                'row': row,
                'col': col,
                'pixel_value': pixel_value,
                'is_valid': is_valid,
                'nodata_value': src.nodata,
                'within_bounds': True
            }
        else:
            return {
                'lat': lat,
                'lon': lon,
                'row': row,
                'col': col,
                'pixel_value': None,
                'is_valid': False,
                'nodata_value': src.nodata,
                'within_bounds': False,
                'error': 'Coordinates outside raster bounds'
            }

def get_raster_info(raster_path):
    """
    Get basic information about the raster file.

    Parameters:
    raster_path (str): Path to the raster file

    Returns:
    dict: Dictionary containing raster metadata
    """
    with rasterio.open(raster_path) as src:
        return {
            'width': src.width,
            'height': src.height,
            'count': src.count,
            'dtype': str(src.dtypes[0]),
            'crs': str(src.crs),
            'bounds': {
                'left': src.bounds.left,
                'bottom': src.bounds.bottom,
                'right': src.bounds.right,
                'top': src.bounds.top
            },
            'transform': list(src.transform),
            'nodata': src.nodata
        }

def save_results_to_json(results, output_file="results.json"):
    """
    Save query results to a JSON file (overwrites existing file).

    Parameters:
    results (list): List of query results
    output_file (str): Output JSON file name (default: "results.json")
    """
    # Prepare data for JSON serialization
    json_data = {
        'timestamp': datetime.now().isoformat(),
        'total_queries': len(results),
        'queries': []
    }

    for result in results:
        # Convert numpy types to Python types for JSON serialization
        query_data = {
            'lat': float(result['lat']),
            'lon': float(result['lon']),
            'row': int(result['row']) if result['row'] is not None else None,
            'col': int(result['col']) if result['col'] is not None else None,
            'pixel_value': float(result['pixel_value']) if result['pixel_value'] is not None and result['pixel_value'] != result['nodata_value'] else None,
            'is_valid': bool(result['is_valid']),
            'within_bounds': bool(result['within_bounds']),
            'error': result.get('error', None)
        }
        json_data['queries'].append(query_data)

    # Write to JSON file (overwrites existing file)
    with open(output_file, 'w') as f:
        json.dump(json_data, f, indent=2)

    print(f"Results saved to {output_file}")
    return output_file

# Example usage and testing
if __name__ == "__main__":
    raster_path = "raster/ET-.tif"

    print("=== RASTER INFO ===")
    info = get_raster_info(raster_path)
    print(f"Dimensions: {info['width']} x {info['height']}")
    print(f"Bounds: {info['bounds']}")
    print(f"NoData: {info['nodata']}")
    print()

    print("=== SAMPLE COORDINATE QUERIES ===")

    # Test coordinates
    test_coords = [
        (31.5204, 74.3587),  # Lahore - should be valid
        (32.0, 72.0),        # Central Pakistan - should be valid
        (33.6844, 73.0479),  # Islamabad - might be NoData
        (25.0, 67.0),        # Outside bounds
        (30.0, 71.5),        # Another test point
    ]

    # Store all results
    all_results = []

    for lat, lon in test_coords:
        result = get_pixel_value_from_coords(raster_path, lat, lon)
        all_results.append(result)

        print(f"Query: Lat={lat}, Lon={lon}")
        print(f"  Pixel position: ({result['row']}, {result['col']})")

        if result['within_bounds']:
            if result['is_valid']:
                print(f"  ✓ Valid ET value: {result['pixel_value']:.2f} mm/day")
            else:
                print(f"  ✗ NoData pixel (value: {result['pixel_value']})")
        else:
            print(f"  ✗ Outside raster bounds")
        print()

    # Save results to JSON file (overwrites existing file)
    print("=== SAVING RESULTS ===")
    save_results_to_json(all_results, "results.json")

    # Also save with raster info
    complete_results = {
        'raster_info': info,
        'query_results': {
            'timestamp': datetime.now().isoformat(),
            'total_queries': len(all_results),
            'queries': []
        }
    }

    for result in all_results:
        query_data = {
            'lat': float(result['lat']),
            'lon': float(result['lon']),
            'row': int(result['row']) if result['row'] is not None else None,
            'col': int(result['col']) if result['col'] is not None else None,
            'pixel_value': float(result['pixel_value']) if result['pixel_value'] is not None and result['pixel_value'] != result['nodata_value'] else None,
            'is_valid': bool(result['is_valid']),
            'within_bounds': bool(result['within_bounds']),
            'error': result.get('error', None)
        }
        complete_results['query_results']['queries'].append(query_data)

    # Save complete results with raster metadata
    with open("complete_results.json", 'w') as f:
        json.dump(complete_results, f, indent=2)

    print("Complete results with raster info saved to complete_results.json")
